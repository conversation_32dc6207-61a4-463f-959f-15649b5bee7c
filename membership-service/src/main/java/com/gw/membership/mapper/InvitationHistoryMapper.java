package com.gw.membership.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gw.membership.dto.InvitationCountResult;
import com.gw.membership.dto.InvitationRecordQuery;
import com.gw.membership.dto.MyInvitationRecordQuery;
import com.gw.membership.entity.InvitationHistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface InvitationHistoryMapper extends BaseMapper<InvitationHistoryEntity> {
    @Select("SELECT * FROM t_invitation_history WHERE invitation_code = #{invitationCode} AND invitee = #{invitee} AND deleted = 0 LIMIT 1")
    InvitationHistoryEntity findFirstByInvitationCodeAndInvitee(@Param("invitationCode") String invitationCode, @Param("invitee") String invitee);

    @Select("SELECT COUNT(*) FROM t_invitation_history WHERE inviter = #{inviter} AND deleted = 0")
    int countByInviter(@Param("inviter") String inviter);

    /**
     * 批量统计用户的邀请数量
     * @param inviters 邀请人列表
     * @return 邀请统计结果列表，包含inviter和count字段
     */
    @Select("<script>SELECT inviter, COUNT(*) as count FROM t_invitation_history " +
            "WHERE inviter IN " +
            "<foreach collection='inviters' item='inviter' open='(' separator=',' close=')'>" +
            "#{inviter}" +
            "</foreach>" +
            " AND deleted = 0 GROUP BY inviter</script>")
    List<InvitationCountResult> batchCountByInviters(@Param("inviters") List<String> inviters);

    List<InvitationHistoryEntity> page(InvitationRecordQuery query);
}
