package com.gw.membership.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gw.common.notify.constant.NotifyConstant;
import com.gw.common.notify.dto.SystemNotifySubmitDTO;
import com.gw.common.notify.service.NotifyProxyService;
import com.gw.membership.constant.OperationTypeConstants;
import com.gw.membership.constant.RewardConstants;
import com.gw.membership.dto.InvitationCodeDTO;
import com.gw.membership.dto.InvitationCountResult;
import com.gw.membership.dto.InvitationRecordQuery;
import com.gw.membership.dto.MyInvitationRecordQuery;
import com.gw.membership.entity.InvitationCodeEntity;
import com.gw.membership.entity.InvitationHistoryEntity;
import com.gw.membership.entity.RewardConfigEntity;
import com.gw.membership.entity.UserMembershipEntity;
import com.gw.membership.mapper.InvitationCodeMapper;
import com.gw.membership.mapper.InvitationHistoryMapper;
import com.gw.membership.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 邀请码服务实现
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class InvitationServiceImpl extends ServiceImpl<InvitationCodeMapper, InvitationCodeEntity> implements InvitationService {
    private final RewardConfigService rewardConfigService;
    private final UserMembershipService userMembershipService;
    private final UserMembershipHistoryService userMembershipHistoryService;
    private final InvitationHistoryMapper invitationHistoryMapper;
    private final NotifyProxyService notifyProxyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvitationCodeEntity createInvitationCode(String username) {
        // 检查用户是否已有邀请码
        InvitationCodeEntity existingCode = getUserInvitationCode(username);
        if (existingCode != null) {
            return existingCode;
        }

        // 生成新地邀请码
        String code = generateUniqueInvitationCode();

        // 创建并保存邀请码
        InvitationCodeEntity entity = new InvitationCodeEntity();
        entity.setUsername(username);
        entity.setCode(code);
        entity.setStatus(1); // 有效
        entity.setUsedTimes(0);
        entity.setMaxUseTimes(-1); // 默认无限制使用次数

        // 设置有效期为2年
        entity.setExpireTime(LocalDateTime.now().plusYears(2));

        this.baseMapper.insert(entity);

        return entity;
    }

    @Override
    public InvitationCodeEntity getUserInvitationCode(String username) {
        LambdaQueryWrapper<InvitationCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InvitationCodeEntity::getUsername, username)
                .eq(InvitationCodeEntity::getStatus, 1)
                .eq(InvitationCodeEntity::getDeleted, 0)
                .orderByDesc(InvitationCodeEntity::getCreateTime);

        return this.baseMapper.selectOne(wrapper);
    }

    @Override
    public boolean validateInvitationCode(String code) {
        LambdaQueryWrapper<InvitationCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InvitationCodeEntity::getCode, code)
                .eq(InvitationCodeEntity::getStatus, 1)
                .eq(InvitationCodeEntity::getDeleted, 0)
                .gt(InvitationCodeEntity::getExpireTime, LocalDateTime.now());

        InvitationCodeEntity entity = this.baseMapper.selectOne(wrapper);

        if (entity == null) {
            return false;
        }

        // 检查是否已达到最大使用次数
        return entity.getMaxUseTimes() == -1 || entity.getUsedTimes() < entity.getMaxUseTimes();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void useInvitationCode(String code, String invitee) {
        RewardConfigEntity configEntity = rewardConfigService.findByCode(RewardConstants.INVITE_CODE);
        if (configEntity == null || configEntity.getStatus() != 1) {
            log.error("没有针对邀请的奖励配置");
            return;
        }
        LambdaQueryWrapper<InvitationCodeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InvitationCodeEntity::getCode, code)
                .eq(InvitationCodeEntity::getStatus, 1)
                .eq(InvitationCodeEntity::getDeleted, 0)
                .gt(InvitationCodeEntity::getExpireTime, LocalDateTime.now());

        InvitationCodeEntity entity = this.baseMapper.selectOne(wrapper);

        if (entity == null) {
            return;
        }

        // 检查是否已达到最大使用次数
        if (entity.getMaxUseTimes() != -1 && entity.getUsedTimes() >= entity.getMaxUseTimes()) {
            return;
        }

        // 更新使用次数
        entity.setUsedTimes(entity.getUsedTimes() + 1);
        this.baseMapper.updateById(entity);

        // 处理邀请奖励
        processInvitationReward(entity.getUsername(), invitee, entity.getCode());

    }

    @Override
    public IPage<InvitationCodeEntity> pageInvitationCodes(String username, Page<InvitationCodeDTO> page) {
        Page<InvitationCodeEntity> entityPage = new Page<>(page.getCurrent(), page.getSize());
        LambdaQueryWrapper<InvitationCodeEntity> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(username != null, InvitationCodeEntity::getUsername, username)
                .eq(InvitationCodeEntity::getDeleted, 0)
                .orderByDesc(InvitationCodeEntity::getCreateTime);

        return this.baseMapper.selectPage(entityPage, wrapper);
    }

    @Override
    public PageInfo<InvitationHistoryEntity> getInvitationRecordPage(int pageNum, int pageSize, InvitationRecordQuery query) {
        PageHelper.startPage(pageNum, pageSize);
        List<InvitationHistoryEntity> list = invitationHistoryMapper.page(query);
        return new PageInfo<>(list);
    }


    @Transactional(rollbackFor = Exception.class)
    public void processInvitationReward(String inviter, String invitee, String inviteCode) {
        // 获取邀请奖励配置

        log.info("处理邀请奖励，邀请人：{}，被邀请人：{}，邀请码：{}", inviter, invitee, inviteCode);
        RewardConfigEntity config = rewardConfigService.findByCode(RewardConstants.INVITE_CODE);
        if (config == null || config.getStatus() != 1) {
            log.warn("邀请奖励配置不存在或未启用");
            return;
        }
        InvitationHistoryEntity invitationHistory = invitationHistoryMapper.findFirstByInvitationCodeAndInvitee(inviteCode, invitee);
        if (invitationHistory != null) {
            log.warn("针对 {} 奖励已经发放", invitee);
            return;
        }
        try {
            // 计算奖励天数
            int validDays = userMembershipService.calculateValidDays(config.getRewardType(), config.getRewardValue());
            if (validDays <= 0) {
                log.warn("邀请奖励天数计算为0，跳过奖励");
                return;
            }


            // 查询或初始化邀请人的会员信息
            UserMembershipEntity userMembership = userMembershipService.findOrInitUserMembership(inviter);

            // 记录变更前的状态
            UserMembershipEntity beforeMembership = userMembershipService.copyUserMembership(userMembership);

            // 直接调用会员服务扩展会员有效期
            boolean result = userMembershipService.extendMembershipFromSystem(inviter, validDays);

            if (result) {
                log.info("成功为用户 {} 激活邀请奖励会员权益，天数: {}", inviter, validDays);

                // 查询更新后的会员信息以记录历史
                UserMembershipEntity afterMembership = userMembershipService.findOrInitUserMembership(inviter);

                // 额外记录邀请获得会员的历史记录
                userMembershipHistoryService.recordMembershipHistory(
                        beforeMembership,
                        afterMembership, OperationTypeConstants.TYPE_INVITATION, // 操作类型: 5-邀请获得
                        null, // 无关联订单
                        "system", invitee, "通过邀请用户 " + invitee + " 获得 " + validDays + " 天会员奖励"
                );
                invitationHistory = new InvitationHistoryEntity(inviter, invitee, inviteCode);
                invitationHistoryMapper.insert(invitationHistory);
                notifyProxyService.insertSystemMessage(new SystemNotifySubmitDTO("邀请奖励", "邀请 " + invitee + " 赠送" + validDays + "天会员时长",
                        NotifyConstant.LEVEL_NORMAL, inviter));
            } else {
                log.error("为用户 {} 激活邀请奖励会员权益失败", inviter);
            }
        } catch (Exception e) {
            log.error("处理邀请奖励时发生错误", e);
            throw e;
        }
    }


    /**
     * 生成唯一的邀请码
     * 使用数据库唯一约束来确保邀请码的唯一性，避免并发问题
     */
    private String generateUniqueInvitationCode() {
        // 最大重试次数
        final int MAX_RETRIES = 5;
        int attempts = 0;

        while (attempts < MAX_RETRIES) {
            try {
                // 生成8位随机字母数字组合的邀请码
                String code = RandomStringUtils.randomAlphanumeric(8).toUpperCase();

                // 尝试插入一个临时记录来检查唯一性
                // 如果插入成功，说明邀请码是唯一的
                int inserted = this.baseMapper.insertCodeCheck(code);

                if (inserted > 0) {
                    // 插入成功，说明邀请码是唯一的
                    return code;
                }

                // 如果插入失败（返回0），说明邀请码已存在，继续尝试
                attempts++;
            } catch (Exception e) {
                // 如果发生异常（如唯一约束冲突），继续尝试
                log.warn("生成邀请码时发生冲突，重试中...", e);
                attempts++;
            }
        }

        // 如果达到最大重试次数仍未成功，使用时间戳作为后缀确保唯一性
        String timestamp = String.valueOf(System.currentTimeMillis()).substring(8);
        String code = RandomStringUtils.randomAlphanumeric(4).toUpperCase() + timestamp;

        log.info("使用带时间戳的邀请码: {}", code);
        return code;
    }

    @Override
    public int countInvitationsByInviter(String username) {
        return invitationHistoryMapper.countByInviter(username);
    }

    @Override
    public Map<String, InvitationCodeEntity> batchGetUserInvitationCodes(List<String> usernames) {
        if (usernames == null || usernames.isEmpty()) {
            return new HashMap<>();
        }

        List<InvitationCodeEntity> codes = this.baseMapper.findByUsernames(usernames);
        return codes.stream()
                .collect(Collectors.toMap(
                    InvitationCodeEntity::getUsername,
                    code -> code,
                    (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
    }

    @Override
    public Map<String, Integer> batchCountInvitationsByInviters(List<String> usernames) {
        if (usernames == null || usernames.isEmpty()) {
            return new HashMap<>();
        }

        List<InvitationCountResult> results = invitationHistoryMapper.batchCountByInviters(usernames);
        Map<String, Integer> countMap = results.stream()
                .collect(Collectors.toMap(
                    InvitationCountResult::getInviter,
                    InvitationCountResult::getCount
                ));

        // 为没有邀请记录的用户设置默认值0
        for (String username : usernames) {
            countMap.putIfAbsent(username, 0);
        }

        return countMap;
    }
}